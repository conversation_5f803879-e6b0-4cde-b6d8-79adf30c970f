-- Add updated_at column to user-websites table
-- This migration adds timestamp tracking for when site records are modified

-- Add the updated_at column with default value
ALTER TABLE "user-websites" 
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT NOW();

-- Update existing records to have the current timestamp
UPDATE "user-websites" 
SET updated_at = NOW() 
WHERE updated_at IS NULL;

-- Create a trigger function to automatically update the timestamp
CREATE OR REPLACE FUNCTION update_user_websites_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS trigger_update_user_websites_updated_at ON "user-websites";
CREATE TRIGGER trigger_update_user_websites_updated_at
  BEFORE UPDATE ON "user-websites"
  FOR EACH ROW
  EXECUTE FUNCTION update_user_websites_updated_at();

-- Add a comment to document the column
COMMENT ON COLUMN "user-websites".updated_at IS 'Timestamp when the site record was last modified';

-- Verify the column was added
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'user-websites' AND column_name = 'updated_at';
