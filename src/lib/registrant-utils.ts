export interface RegistrantInfo {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  address1: string;
  city: string;
  state_province: string;
  postal_code: string;
  country: string;
}

export interface RegistrantValidationResult {
  isComplete: boolean;
  missingFields: string[];
  registrantData?: RegistrantInfo;
}

/**
 * Required fields for domain registration
 */
export const REQUIRED_REGISTRANT_FIELDS = [
  'first_name',
  'last_name', 
  'email',
  'phone',
  'address1',
  'city',
  'state_province',
  'postal_code',
  'country'
] as const;

/**
 * Field labels for display purposes
 */
export const FIELD_LABELS: Record<string, string> = {
  first_name: 'First Name',
  last_name: 'Last Name',
  email: 'Email Address',
  phone: 'Phone Number',
  address1: 'Street Address',
  city: 'City',
  state_province: 'State/Province',
  postal_code: 'Postal Code',
  country: 'Country'
};

/**
 * Validates if all required registrant fields are present and non-empty
 * @param profile - User profile data from database
 * @returns Validation result with missing fields and complete registrant data
 */
export function validateRegistrantInfo(profile: any): RegistrantValidationResult {
  const missingFields: string[] = [];
  const registrantData: Partial<RegistrantInfo> = {};

  // Check each required field
  for (const field of REQUIRED_REGISTRANT_FIELDS) {
    const value = profile?.[field];
    
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      missingFields.push(field);
    } else {
      registrantData[field as keyof RegistrantInfo] = value;
    }
  }

  return {
    isComplete: missingFields.length === 0,
    missingFields,
    registrantData: missingFields.length === 0 ? registrantData as RegistrantInfo : undefined
  };
}

/**
 * Formats registrant data for Namecheap API
 * @param registrantData - Complete registrant information
 * @returns Formatted data for Namecheap domain registration
 */
export function formatRegistrantForNamecheap(registrantData: RegistrantInfo) {
  return {
    FirstName: registrantData.first_name,
    LastName: registrantData.last_name,
    Address1: registrantData.address1,
    City: registrantData.city,
    StateProvince: registrantData.state_province,
    PostalCode: registrantData.postal_code,
    Country: registrantData.country,
    Phone: registrantData.phone,
    EmailAddress: registrantData.email,
  };
}

/**
 * Gets missing field labels for display
 * @param missingFields - Array of missing field names
 * @returns Array of human-readable field labels
 */
export function getMissingFieldLabels(missingFields: string[]): string[] {
  return missingFields.map(field => FIELD_LABELS[field] || field);
}
