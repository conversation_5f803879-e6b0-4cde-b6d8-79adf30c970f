'use client';

import React, { useState, useEffect } from 'react';
import { X, User, Mail, Phone, MapPin, Globe, AlertCircle } from 'lucide-react';
import { FIELD_LABELS, getMissingFieldLabels } from '@/lib/registrant-utils';
import { useAuth } from './providers/AuthProvider';

interface RegistrantInfoModalProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: (updatedProfile: any) => void;
  missingFields: string[];
  currentProfile: any;
}

interface FormData {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  address1: string;
  city: string;
  state_province: string;
  postal_code: string;
  country: string;
}

const RegistrantInfoModal: React.FC<RegistrantInfoModalProps> = ({
  isOpen,
  onClose,
  onComplete,
  missingFields,
  currentProfile
}) => {
  const { updateProfile } = useAuth();
  const [formData, setFormData] = useState<FormData>({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    address1: '',
    city: '',
    state_province: '',
    postal_code: '',
    country: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize form data with current profile values
  useEffect(() => {
    if (currentProfile) {
      setFormData({
        first_name: currentProfile.first_name || '',
        last_name: currentProfile.last_name || '',
        email: currentProfile.email || '',
        phone: currentProfile.phone || '',
        address1: currentProfile.address1 || '',
        city: currentProfile.city || '',
        state_province: currentProfile.state_province || '',
        postal_code: currentProfile.postal_code || '',
        country: currentProfile.country || ''
      });
    }
  }, [currentProfile]);

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear error when user starts typing
    if (error) setError(null);
  };

  const validateForm = (): boolean => {
    for (const field of missingFields) {
      const value = formData[field as keyof FormData];
      if (!value || value.trim() === '') {
        setError(`Please fill in ${FIELD_LABELS[field]}`);
        return false;
      }
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Update the profile in the database
      await updateProfile(formData);
      
      // Call onComplete with the updated profile data
      onComplete({ ...currentProfile, ...formData });
    } catch (err: any) {
      console.error('Error updating profile:', err);
      setError(err.message || 'Failed to update profile information');
    } finally {
      setLoading(false);
    }
  };

  const getFieldIcon = (field: string) => {
    switch (field) {
      case 'first_name':
      case 'last_name':
        return <User className="w-4 h-4 text-gray-400" />;
      case 'email':
        return <Mail className="w-4 h-4 text-gray-400" />;
      case 'phone':
        return <Phone className="w-4 h-4 text-gray-400" />;
      case 'address1':
      case 'city':
      case 'state_province':
      case 'postal_code':
        return <MapPin className="w-4 h-4 text-gray-400" />;
      case 'country':
        return <Globe className="w-4 h-4 text-gray-400" />;
      default:
        return <User className="w-4 h-4 text-gray-400" />;
    }
  };

  if (!isOpen) return null;

  const missingFieldLabels = getMissingFieldLabels(missingFields);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="relative w-full max-w-2xl max-h-[90vh] overflow-y-auto bg-white rounded-lg shadow-xl">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Complete Your Account Information
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                We need some additional information to register your domain
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="px-6 py-4">
          {/* Missing fields notice */}
          <div className="mb-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
            <div className="flex items-start">
              <AlertCircle className="w-5 h-5 text-amber-600 mt-0.5 mr-3 flex-shrink-0" />
              <div>
                <h3 className="text-sm font-medium text-amber-800">
                  Missing Required Information
                </h3>
                <p className="text-sm text-amber-700 mt-1">
                  Please provide the following information: {missingFieldLabels.join(', ')}
                </p>
              </div>
            </div>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {missingFields.map((field) => (
                <div key={field} className={field === 'address1' ? 'md:col-span-2' : ''}>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {FIELD_LABELS[field]} <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <div className="absolute left-3 top-3">
                      {getFieldIcon(field)}
                    </div>
                    <input
                      type={field === 'email' ? 'email' : field === 'phone' ? 'tel' : 'text'}
                      value={formData[field as keyof FormData]}
                      onChange={(e) => handleInputChange(field as keyof FormData, e.target.value)}
                      className="w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                      placeholder={`Enter your ${FIELD_LABELS[field].toLowerCase()}`}
                      required
                    />
                  </div>
                </div>
              ))}
            </div>

            {/* Error message */}
            {error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            {/* Footer */}
            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                disabled={loading}
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {loading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Updating...
                  </>
                ) : (
                  'Save & Continue'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default RegistrantInfoModal;
