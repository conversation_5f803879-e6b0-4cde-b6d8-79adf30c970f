"use client";

import { useEffect, useState, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from '../../../../components/providers/AuthProvider';
import DomainSiteMappingStep from '../../../../components/domain/DomainSiteMappingStep';
import RegistrantInfoModal from '../../../../components/RegistrantInfoModal';

export default function DomainCompletePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const sessionId = searchParams.get("session_id");
  const domain = searchParams.get("domain");
  const siteId = searchParams.get("siteId");

  const { user, session, loading: authLoading } = useAuth();

  const [status, setStatus] = useState("Processing your domain registration...");
  const [error, setError] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState<'registration' | 'mapping' | 'complete'>('registration');
  const [registeredDomain, setRegisteredDomain] = useState<string | null>(null);
  const [showRegistrantModal, setShowRegistrantModal] = useState(false);
  const [missingFields, setMissingFields] = useState<string[]>([]);
  const [currentProfile, setCurrentProfile] = useState<any>(null);
  const hasRun = useRef(false);

  useEffect(() => {
    const processDomainRegistration = async () => {
      if (hasRun.current || authLoading || !user) {
        console.log('Conditions not met for registration:', { hasRun: hasRun.current, authLoading, user: !!user });
        return;
      }
      hasRun.current = true;
      if (!sessionId || !domain) {
        setError("Missing required parameters (session ID or domain).");
        return;
      }

      console.log('Starting domain registration process:', { sessionId, domain, siteId, userId: user.id });

      // Skip payment verification - proceed directly to domain registration
      // Since Stripe redirected to success URL, we trust the payment was successful
      try {
        console.log('Checking registrant information...');
        setStatus("Checking your account information...");

        // First, check if registrant information is complete
        const registrantCheckRes = await fetch(`/api/registrant-info?userId=${user.id}`);
        const registrantData = await registrantCheckRes.json();

        if (!registrantCheckRes.ok) {
          throw new Error(registrantData.error || "Failed to check registrant information");
        }

        if (!registrantData.isComplete) {
          // Show modal to collect missing information
          setMissingFields(registrantData.missingFields);
          setCurrentProfile(registrantData.profile);
          setShowRegistrantModal(true);
          setStatus("Please complete your account information to continue...");
          return;
        }

        console.log('Proceeding with domain registration...');
        setStatus("Registering your domain...");
        // Register domain directly with Namecheap
        const registerRes = await fetch("/api/namecheap", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            domain,
            action: "register",
            siteId: siteId || 'pending',
            userId: user.id,
            stripeSessionId: sessionId
          }),
        });

        console.log('Domain registration response status:', registerRes.status);

        const registerData = await registerRes.json();
        console.log('Domain registration data:', registerData);

        if (!registerRes.ok || registerData.error) {
          throw new Error(registerData.error || "Domain registration failed.");
        }

        // Domain registration successful - move to site mapping step
        setRegisteredDomain(domain);
        setCurrentStep('mapping');
        setStatus("Domain registered successfully! Now let's connect it to your site.");
      } catch (err: any) {
        console.error('Domain registration error:', err);
        setError(err.message || "An error occurred.");
        setStatus("");
      }
    };
    processDomainRegistration();
  }, [sessionId, domain, siteId, authLoading, user, router]);

  const handleRegistrantInfoComplete = async (updatedProfile: any) => {
    setShowRegistrantModal(false);
    setCurrentProfile(updatedProfile);

    try {
      console.log('Proceeding with domain registration after profile update...');
      setStatus("Registering your domain...");

      // Now proceed with domain registration
      const registerRes = await fetch("/api/namecheap", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          domain,
          action: "register",
          siteId: siteId || 'pending',
          userId: user!.id,
          stripeSessionId: sessionId
        }),
      });

      console.log('Domain registration response status:', registerRes.status);

      const registerData = await registerRes.json();
      console.log('Domain registration data:', registerData);

      if (!registerRes.ok || registerData.error) {
        throw new Error(registerData.error || "Domain registration failed.");
      }

      // Domain registration successful - move to site mapping step
      setRegisteredDomain(domain!);
      setCurrentStep('mapping');
      setStatus("Domain registered successfully! Now let's connect it to your site.");
    } catch (err: any) {
      console.error('Domain registration error:', err);
      setError(err.message || "An error occurred.");
      setStatus("");
    }
  };

  // Handle site mapping completion
  const handleSiteMappingComplete = async (selectedSiteId: string) => {
    if (!registeredDomain || !user || !session) {
      throw new Error('Missing domain, user, or session information');
    }

    setStatus("Configuring DNS and mapping domain to site...");

    try {
      // Get the user's session token for authentication
      const token = session.access_token;

      if (!token) {
        throw new Error('Authentication token not available');
      }

      const response = await fetch('/api/domain-mapping', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          domainName: registeredDomain,
          siteId: selectedSiteId
        }),
      });

      const data = await response.json();

      if (!response.ok || data.error) {
        throw new Error(data.error || 'Failed to map domain to site');
      }

      setCurrentStep('complete');
      setStatus("Domain setup complete! Redirecting to dashboard...");
      setTimeout(() => router.replace("/dashboard"), 2000);

    } catch (error: any) {
      console.error('Site mapping error:', error);
      throw error; // Re-throw to be handled by the DomainSiteMappingStep component
    }
  };

  if (authLoading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 p-4">
        <div className="bg-white p-6 sm:p-8 rounded-lg shadow-md max-w-md w-full text-center">
          <h1 className="text-xl sm:text-2xl font-bold mb-4 text-gray-800">Verifying Authentication</h1>
          <p>Please wait while we confirm your session...</p>
        </div>
      </div>
    );
  }

  // Show site mapping step if domain registration is complete
  if (currentStep === 'mapping' && registeredDomain) {
    return (
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-4xl mx-auto">
          <DomainSiteMappingStep
            domainName={registeredDomain}
            onComplete={handleSiteMappingComplete}
          />
        </div>
      </div>
    );
  }

  // Show registration progress or completion
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 p-4">
      <div className="bg-white p-6 sm:p-8 rounded-lg shadow-md max-w-md w-full text-center">
        <h1 className="text-xl sm:text-2xl font-bold mb-4 text-gray-800">
          {currentStep === 'complete' ? 'Domain Setup Complete!' : 'Completing Domain Registration'}
        </h1>
        {status && <p className="mb-4 text-sm sm:text-base text-gray-700">{status}</p>}
        {error && (
          <div className="text-red-600 font-semibold mb-4 text-sm sm:text-base break-words">{error}</div>
        )}
        {!error && currentStep !== 'complete' && (
          <div className="flex items-center justify-center mb-4">
            <svg className="animate-spin h-6 w-6 sm:h-8 sm:w-8 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
            </svg>
          </div>
        )}
        {currentStep === 'complete' && (
          <div className="flex items-center justify-center mb-4">
            <svg className="h-12 w-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
        )}
        {error && (
          <button
            className="w-full sm:w-auto mt-4 px-4 py-2 text-sm sm:text-base bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            onClick={() => router.replace("/dashboard/domain")}
          >
            Back to Domain Page
          </button>
        )}
      </div>

      {/* Registrant Info Modal */}
      <RegistrantInfoModal
        isOpen={showRegistrantModal}
        onClose={() => setShowRegistrantModal(false)}
        onComplete={handleRegistrantInfoComplete}
        missingFields={missingFields}
        currentProfile={currentProfile}
      />
    </div>
  );
}