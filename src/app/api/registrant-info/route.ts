import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { validateRegistrantInfo } from '@/lib/registrant-utils';

// Helper to get Supabase client (service role)
function getSupabaseServiceClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  );
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({ error: 'userId is required' }, { status: 400 });
    }

    const supabase = getSupabaseServiceClient();

    // Get user email
    const { data: userData, error: userError } = await supabase.auth.admin.getUserById(userId);
    if (userError || !userData.user) {
      return NextResponse.json({ error: 'Failed to fetch user information' }, { status: 400 });
    }

    const userEmail = userData.user.email;

    // Get user profile with all registrant fields
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('first_name, last_name, email, phone, address1, city, state_province, postal_code, country')
      .eq('email', userEmail)
      .single();

    if (profileError) {
      return NextResponse.json({ error: `Failed to fetch user profile: ${profileError.message}` }, { status: 400 });
    }

    // Validate registrant information
    const validation = validateRegistrantInfo(profile);

    return NextResponse.json({
      isComplete: validation.isComplete,
      missingFields: validation.missingFields,
      profile: profile
    });

  } catch (error: any) {
    console.error('Error checking registrant info:', error);
    return NextResponse.json({ 
      error: error.message || 'Internal server error' 
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId, profileData } = await request.json();

    if (!userId || !profileData) {
      return NextResponse.json({ error: 'userId and profileData are required' }, { status: 400 });
    }

    const supabase = getSupabaseServiceClient();

    // Get user email
    const { data: userData, error: userError } = await supabase.auth.admin.getUserById(userId);
    if (userError || !userData.user) {
      return NextResponse.json({ error: 'Failed to fetch user information' }, { status: 400 });
    }

    const userEmail = userData.user.email;

    // Update user profile
    const { data: updatedProfile, error: updateError } = await supabase
      .from('profiles')
      .update(profileData)
      .eq('email', userEmail)
      .select()
      .single();

    if (updateError) {
      return NextResponse.json({ error: `Failed to update profile: ${updateError.message}` }, { status: 400 });
    }

    // Validate the updated information
    const validation = validateRegistrantInfo(updatedProfile);

    return NextResponse.json({
      success: true,
      isComplete: validation.isComplete,
      missingFields: validation.missingFields,
      profile: updatedProfile
    });

  } catch (error: any) {
    console.error('Error updating registrant info:', error);
    return NextResponse.json({ 
      error: error.message || 'Internal server error' 
    }, { status: 500 });
  }
}
